# Private Area - Access Restricted
# Heart & Soul Film- und Medienproduktion

# Deny access to all files by default
Order Deny,Allow
Deny from all

# Allow access only from specific IP addresses (add your IPs here)
# Allow from 127.0.0.1
# Allow from YOUR_IP_ADDRESS

# Password protection for admin area
AuthType Basic
AuthName "Heart & Soul Admin Area"
AuthUserFile /path/to/.htpasswd
Require valid-user

# Prevent directory browsing
Options -Indexes

# Protect sensitive files
<Files "*.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "*.sql">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.txt">
    Order Deny,Allow
    Deny from all
</Files>

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
