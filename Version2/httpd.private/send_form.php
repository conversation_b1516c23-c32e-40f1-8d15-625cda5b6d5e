<?php
/**
 * Form Submission Handler
 * 
 * This script processes form submissions and sends them via <NAME_EMAIL>
 * It includes security measures to prevent common vulnerabilities.
 */

// Set strict error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', '../httpd.private/logs/php_errors.log');

// Configuration
$recipient_email = '<EMAIL>';
$site_name = 'Heart & Soul Medienproduktion';
$max_email_length = 1000000; // Prevent extremely large emails

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Content-Security-Policy: default-src \'self\'');

/**
 * Sanitize and validate input
 * 
 * @param string $data Input data to sanitize
 * @return string Sanitized data
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Validate email address
 * 
 * @param string $email Email to validate
 * @return bool True if valid, false otherwise
 */
function is_valid_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Check for header injection attempts
 * 
 * @param string $str String to check
 * @return bool True if injection detected, false otherwise
 */
function has_header_injection($str) {
    return preg_match('/[\r\n]/', $str) !== 0;
}

/**
 * Check honeypot field
 * 
 * @return bool True if honeypot is empty (human), false if filled (bot)
 */
function check_honeypot() {
    return !isset($_POST['website']) || $_POST['website'] === '';
}

/**
 * Send email securely
 * 
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $message Email message
 * @param string $from_email Sender email
 * @param string $from_name Sender name
 * @return bool True if sent successfully, false otherwise
 */
function send_secure_email($to, $subject, $message, $from_email, $from_name) {
    // Validate inputs to prevent header injection
    if (has_header_injection($to) || has_header_injection($subject) || 
        has_header_injection($from_email) || has_header_injection($from_name)) {
        error_log('Header injection attempt detected');
        return false;
    }
    
    // Validate email addresses
    if (!is_valid_email($to) || !is_valid_email($from_email)) {
        error_log('Invalid email address detected');
        return false;
    }
    
    // Prepare headers
    $headers = "From: " . $from_name . " <" . $from_email . ">\r\n";
    $headers .= "Reply-To: " . $from_email . "\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    // Limit message size
    if (strlen($message) > $GLOBALS['max_email_length']) {
        $message = substr($message, 0, $GLOBALS['max_email_length']) . '... [Content truncated due to size]';
    }
    
    // Send email
    return mail($to, $subject, $message, $headers);
}

// Initialize response array
$response = [
    'success' => false,
    'message' => 'An error occurred'
];

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    // Check honeypot
    if (!check_honeypot()) {
        // Don't reveal that we detected a bot
        $response['success'] = true;
        $response['message'] = 'Form processed successfully';
        echo json_encode($response);
        exit;
    }
    
    // Validate required fields
    $required_fields = ['form_type', 'form_data'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            $response['message'] = 'Missing required fields';
            echo json_encode($response);
            exit;
        }
    }
    
    // Sanitize inputs
    $form_type = sanitize_input($_POST['form_type']);
    $form_data = json_decode($_POST['form_data'], true);
    
    // Validate form data
    if (!is_array($form_data)) {
        $response['message'] = 'Invalid form data format';
        echo json_encode($response);
        exit;
    }
    
    // Sanitize all form data fields
    foreach ($form_data as $key => $value) {
        if (is_string($value)) {
            $form_data[$key] = sanitize_input($value);
        }
    }
    
    // Prepare email content based on form type
    $subject = '';
    $message = '';
    $from_email = '<EMAIL>';
    $from_name = 'Heart & Soul Medienproduktion Website';
    
    if ($form_type === 'meeting') {
        $subject = 'Neue Terminanfrage von ' . ($form_data['name'] ?? 'Unbekannt');
        
        // Format date if available
        $meeting_date = '';
        if (isset($form_data['date']) && isset($form_data['time'])) {
            $meeting_date = $form_data['date'] . ' um ' . $form_data['time'];
        }
        
        // Build HTML message
        $message = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>' . $subject . '</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; border-left: 4px solid #ff3030;">
                <h2 style="color: #ff3030; margin-top: 0;">Neue Terminanfrage</h2>
                <p>Eine neue Terminanfrage wurde über das Formular auf der Website eingereicht.</p>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Kontaktdaten:</h3>
                <ul style="list-style-type: none; padding-left: 0;">
                    <li><strong>Name:</strong> ' . ($form_data['name'] ?? 'Nicht angegeben') . '</li>
                    <li><strong>Firma:</strong> ' . ($form_data['company'] ?? 'Nicht angegeben') . '</li>
                    <li><strong>E-Mail:</strong> ' . ($form_data['email'] ?? 'Nicht angegeben') . '</li>
                    <li><strong>Telefon:</strong> ' . ($form_data['phone'] ?? 'Nicht angegeben') . '</li>
                </ul>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Termindetails:</h3>
                <ul style="list-style-type: none; padding-left: 0;">
                    <li><strong>Gewünschter Termin:</strong> ' . $meeting_date . '</li>
                    <li><strong>Eingegangen am:</strong> ' . date('d.m.Y H:i:s') . '</li>
                </ul>
            </div>
            
            <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 20px;">
                <p>Diese E-Mail wurde automatisch vom Heart & Soul Medienproduktion Website-Formular gesendet.</p>
            </div>
        </body>
        </html>';
    } 
    elseif ($form_type === 'contact') {
        $subject = 'Neue Kontaktanfrage von ' . ($form_data['name'] ?? 'Unbekannt');
        
        // Build HTML message
        $message = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>' . $subject . '</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; border-left: 4px solid #ff3030;">
                <h2 style="color: #ff3030; margin-top: 0;">Neue Kontaktanfrage</h2>
                <p>Eine neue Kontaktanfrage wurde über das Formular auf der Website eingereicht.</p>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Kontaktdaten:</h3>
                <ul style="list-style-type: none; padding-left: 0;">
                    <li><strong>Name:</strong> ' . ($form_data['name'] ?? 'Nicht angegeben') . '</li>
                    ' . (isset($form_data['company']) && !empty($form_data['company']) ? '<li><strong>Firma:</strong> ' . $form_data['company'] . '</li>' : '') . '
                    <li><strong>E-Mail:</strong> ' . ($form_data['email'] ?? 'Nicht angegeben') . '</li>
                    ' . (isset($form_data['phone']) && !empty($form_data['phone']) ? '<li><strong>Telefon:</strong> ' . $form_data['phone'] . '</li>' : '') . '
                </ul>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Anfrage:</h3>
                <p><strong>Betreff:</strong> ' . ($form_data['subject'] ?? 'Kein Betreff') . '</p>
                <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <p>' . nl2br($form_data['message'] ?? 'Keine Nachricht') . '</p>
                </div>
                <p><strong>Eingegangen am:</strong> ' . date('d.m.Y H:i:s') . '</p>
            </div>
            
            <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 20px;">
                <p>Diese E-Mail wurde automatisch vom Heart & Soul Medienproduktion Website-Formular gesendet.</p>
            </div>
        </body>
        </html>';
    }
    elseif ($form_type === 'calculator') {
        $subject = 'Neue Filmkalkulator-Anfrage von ' . ($form_data['name'] ?? 'Unbekannt');
        
        // Build HTML message
        $message = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>' . $subject . '</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; border-left: 4px solid #ff3030;">
                <h2 style="color: #ff3030; margin-top: 0;">Neue Filmkalkulator-Anfrage</h2>
                <p>Eine neue Anfrage wurde über den Filmkalkulator auf der Website eingereicht.</p>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Kontaktdaten:</h3>
                <ul style="list-style-type: none; padding-left: 0;">
                    <li><strong>Name:</strong> ' . ($form_data['name'] ?? 'Nicht angegeben') . '</li>
                    <li><strong>Firma:</strong> ' . ($form_data['company'] ?? 'Nicht angegeben') . '</li>
                    <li><strong>E-Mail:</strong> ' . ($form_data['email'] ?? 'Nicht angegeben') . '</li>
                    <li><strong>Telefon:</strong> ' . ($form_data['phone'] ?? 'Nicht angegeben') . '</li>
                </ul>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Projektdetails:</h3>
                <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                    <tbody>';
        
        // Add all form fields to the table
        foreach ($form_data as $key => $value) {
            if ($key !== 'name' && $key !== 'company' && $key !== 'email' && $key !== 'phone') {
                $message .= '
                        <tr>
                            <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>' . ucfirst(str_replace('_', ' ', $key)) . ':</strong></td>
                            <td style="padding: 8px; border-bottom: 1px solid #eee;">' . $value . '</td>
                        </tr>';
            }
        }
        
        $message .= '
                    </tbody>
                </table>
                <p><strong>Eingegangen am:</strong> ' . date('d.m.Y H:i:s') . '</p>
            </div>
            
            <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 20px;">
                <p>Diese E-Mail wurde automatisch vom Heart & Soul Medienproduktion Filmkalkulator gesendet.</p>
            </div>
        </body>
        </html>';
    }
    else {
        $response['message'] = 'Invalid form type';
        echo json_encode($response);
        exit;
    }
    
    // Send email
    $email_sent = send_secure_email(
        $recipient_email,
        $subject,
        $message,
        $from_email,
        $from_name
    );
    
    if ($email_sent) {
        $response['success'] = true;
        $response['message'] = 'Email sent successfully';
    } else {
        $response['message'] = 'Failed to send email';
        error_log('Failed to send email to ' . $recipient_email);
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
