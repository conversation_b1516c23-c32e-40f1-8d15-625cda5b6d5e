<?php
session_start();

// Database configuration (replace with your actual database credentials)
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'your_database');

// Initialize database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    error_log('Connection failed: ' . $e->getMessage());
    http_response_code(500);
    exit('Database connection error');
}

// Rate limiting
function checkRateLimit($ip) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM login_attempts WHERE ip = ? AND timestamp > DATE_SUB(NOW(), INTERVAL 15 MINUTE)");
    $stmt->execute([$ip]);
    return $stmt->fetchColumn() < 5; // Allow 5 attempts per 15 minutes
}

// Record login attempt
function recordLoginAttempt($ip, $success) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO login_attempts (ip, success, timestamp) VALUES (?, ?, NOW())");
    $stmt->execute([$ip, $success]);
}

// Generate and store reset token
function generateResetToken($email) {
    global $pdo;
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    $stmt = $pdo->prepare("INSERT INTO password_resets (email, token, expires) VALUES (?, ?, ?)");
    $stmt->execute([$email, $token, $expires]);
    
    return $token;
}

// Verify reset token
function verifyResetToken($token) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT email FROM password_resets WHERE token = ? AND expires > NOW() AND used = 0");
    $stmt->execute([$token]);
    return $stmt->fetchColumn();
}

// Handle login request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'login') {
    $response = ['success' => false, 'message' => ''];
    
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $response['message'] = 'Invalid request';
        echo json_encode($response);
        exit;
    }
    
    // Check rate limit
    if (!checkRateLimit($_SERVER['REMOTE_ADDR'])) {
        $response['message'] = 'Too many login attempts. Please try again later.';
        echo json_encode($response);
        exit;
    }
    
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    $stmt = $pdo->prepare("SELECT id, password_hash FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password_hash'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['last_activity'] = time();
        recordLoginAttempt($_SERVER['REMOTE_ADDR'], true);
        $response['success'] = true;
    } else {
        recordLoginAttempt($_SERVER['REMOTE_ADDR'], false);
        $response['message'] = 'Invalid credentials';
    }
    
    echo json_encode($response);
    exit;
}

// Handle password reset request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'reset_request') {
    $response = ['success' => false, 'message' => ''];
    
    $email = $_POST['email'] ?? '';
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    
    if ($stmt->fetch()) {
        $token = generateResetToken($email);
        
        // Send reset email (implement your email sending logic here)
        $resetLink = 'https://' . $_SERVER['HTTP_HOST'] . '/admin/reset-password.php?token=' . $token;
        $to = $email;
        $subject = 'Password Reset Request';
        $message = "Please click the following link to reset your password: \n\n$resetLink\n\nThis link will expire in 1 hour.";
        $headers = 'From: <EMAIL>';
        
        mail($to, $subject, $message, $headers);
        
        $response['success'] = true;
        $response['message'] = 'If an account exists with this email, you will receive reset instructions.';
    } else {
        $response['success'] = true; // Same response for security
        $response['message'] = 'If an account exists with this email, you will receive reset instructions.';
    }
    
    echo json_encode($response);
    exit;
}

// Handle password reset
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'reset_password') {
    $response = ['success' => false, 'message' => ''];
    
    $token = $_POST['token'] ?? '';
    $password = $_POST['password'] ?? '';
    
    $email = verifyResetToken($token);
    if ($email) {
        $password_hash = password_hash($password, PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
        $stmt->execute([$password_hash, $email]);
        
        $stmt = $pdo->prepare("UPDATE password_resets SET used = 1 WHERE token = ?");
        $stmt->execute([$token]);
        
        $response['success'] = true;
        $response['message'] = 'Password has been reset successfully';
    } else {
        $response['message'] = 'Invalid or expired reset token';
    }
    
    echo json_encode($response);
    exit;
}