document.addEventListener('DOMContentLoaded', function() {
    const resetForm = document.getElementById('reset-form');
    const resetError = document.getElementById('reset-error');
    const resetSuccess = document.getElementById('reset-success');
    const errorText = document.getElementById('error-text');
    const successText = document.getElementById('success-text');
    const passwordToggles = document.querySelectorAll('.password-toggle');

    // Password visibility toggle
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
            input.setAttribute('type', type);

            // Toggle icon
            const icon = this.querySelector('i');
            if (type === 'text') {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // Password validation
    function validatePassword(password) {
        const minLength = 12;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

        if (password.length < minLength) {
            return 'Das Passwort muss mindestens 12 Zeichen lang sein.';
        }
        if (!hasUpperCase || !hasLowerCase) {
            return 'Das Passwort muss Groß- und Kleinbuchstaben enthalten.';
        }
        if (!hasNumbers) {
            return 'Das Passwort muss mindestens eine Zahl enthalten.';
        }
        if (!hasSpecialChar) {
            return 'Das Passwort muss mindestens ein Sonderzeichen enthalten.';
        }
        return '';
    }

    // Handle form submission
    resetForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        const token = document.getElementById('reset-token').value;

        // Hide any previous messages
        resetError.style.display = 'none';
        resetSuccess.style.display = 'none';

        // Validate password
        const passwordError = validatePassword(password);
        if (passwordError) {
            errorText.textContent = passwordError;
            resetError.style.display = 'block';
            return;
        }

        // Check if passwords match
        if (password !== confirmPassword) {
            errorText.textContent = 'Die Passwörter stimmen nicht überein.';
            resetError.style.display = 'block';
            return;
        }

        try {
            const response = await fetch('auth.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'reset_password',
                    token: token,
                    password: password
                })
            });

            const data = await response.json();

            if (data.success) {
                successText.textContent = data.message;
                resetSuccess.style.display = 'block';
                resetForm.reset();

                // Redirect to login page after 3 seconds
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 3000);
            } else {
                errorText.textContent = data.message || 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.';
                resetError.style.display = 'block';
            }
        } catch (error) {
            console.error('Error:', error);
            errorText.textContent = 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.';
            resetError.style.display = 'block';
        }
    });
});