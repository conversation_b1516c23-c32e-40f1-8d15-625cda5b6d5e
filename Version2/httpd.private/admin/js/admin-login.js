document.addEventListener('DOMContentLoaded', function() {
  const loginForm = document.getElementById('login-form');
  const forgotPasswordForm = document.getElementById('forgot-password-form');
  const loginError = document.getElementById('login-error');
  const passwordToggle = document.querySelector('.password-toggle');
  const passwordInput = document.getElementById('password');
  const forgotPasswordLink = document.getElementById('forgot-password-link');
  const backToLoginLink = document.getElementById('back-to-login-link');

  // Check if user is already logged in
  if (localStorage.getItem('adminLoggedIn') === 'true') {
    window.location.href = 'dashboard.php';
  }

  // Handle password visibility toggle
  if (passwordToggle) {
    passwordToggle.addEventListener('click', function() {
      const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
      passwordInput.setAttribute('type', type);

      // Toggle icon
      const icon = this.querySelector('i');
      if (type === 'text') {
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }
    });
  }

  // Add input focus animations
  const formInputs = document.querySelectorAll('.form-group input');
  formInputs.forEach(input => {
    input.addEventListener('focus', function() {
      this.parentElement.classList.add('input-focused');
    });

    input.addEventListener('blur', function() {
      if (!this.value) {
        this.parentElement.classList.remove('input-focused');
      }
    });
  });

  // Toggle between login and forgot password forms
  forgotPasswordLink.addEventListener('click', function(e) {
    e.preventDefault();
    loginForm.style.display = 'none';
    forgotPasswordForm.style.display = 'block';
    loginError.style.display = 'none';
  });

  backToLoginLink.addEventListener('click', function(e) {
    e.preventDefault();
    forgotPasswordForm.style.display = 'none';
    loginForm.style.display = 'block';
    loginError.style.display = 'none';
  });

  // Handle login form submission
  loginForm.addEventListener('submit', async function(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const csrfToken = document.getElementById('csrf-token')?.content;

    try {
      const response = await fetch('auth.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'login',
          username: username,
          password: password,
          csrf_token: csrfToken
        })
      });

      const data = await response.json();

      if (data.success) {
        window.location.href = 'dashboard.php';
      } else {
        // Show error message with animation
        loginError.textContent = data.message || 'Ungültige Anmeldedaten. Bitte versuchen Sie es erneut.';
        loginError.style.display = 'block';

        // Reset animation to trigger it again if already visible
        loginError.style.animation = 'none';
        setTimeout(() => {
          loginError.style.animation = 'shake 0.5s ease-in-out';
        }, 10);

        // Clear password field
        passwordInput.value = '';
      }
    } catch (error) {
      console.error('Error:', error);
      loginError.textContent = 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.';
      loginError.style.display = 'block';
    }
  });

  // Handle forgot password form submission
  forgotPasswordForm.addEventListener('submit', async function(e) {
    e.preventDefault();

    const email = document.getElementById('email').value;

    try {
      const response = await fetch('auth.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'reset_request',
          email: email
        })
      });

      const data = await response.json();

      // Always show success message for security (don't reveal if email exists)
      loginError.textContent = data.message || 'Wenn ein Konto mit dieser E-Mail-Adresse existiert, erhalten Sie eine E-Mail mit Anweisungen zum Zurücksetzen des Passworts.';
      loginError.style.display = 'block';
      loginError.style.backgroundColor = '#4CAF50';
      loginError.style.color = 'white';

      // Clear email field
      document.getElementById('email').value = '';

      // Switch back to login form after 3 seconds
      setTimeout(() => {
        forgotPasswordForm.style.display = 'none';
        loginForm.style.display = 'block';
        loginError.style.backgroundColor = '';
        loginError.style.color = '';
      }, 3000);
    } catch (error) {
      console.error('Error:', error);
      loginError.textContent = 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.';
      loginError.style.display = 'block';
    }
  });
});
