<?php
/**
 * Simple Form Test Script
 * This script tests if PHP form processing works on the server
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type
header('Content-Type: application/json');

// Initialize response
$response = [
    'success' => false,
    'message' => 'Unknown error',
    'debug' => []
];

// Add debug information
$response['debug']['php_version'] = phpversion();
$response['debug']['request_method'] = $_SERVER['REQUEST_METHOD'];
$response['debug']['post_data'] = $_POST;
$response['debug']['mail_function'] = function_exists('mail') ? 'available' : 'not available';

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response['debug']['post_received'] = true;
    
    // Check if form_type is set
    if (isset($_POST['form_type'])) {
        $response['debug']['form_type'] = $_POST['form_type'];
        
        // Try to decode form_data
        if (isset($_POST['form_data'])) {
            $form_data = json_decode($_POST['form_data'], true);
            if ($form_data) {
                $response['debug']['form_data_decoded'] = true;
                $response['debug']['form_fields'] = array_keys($form_data);
                
                // Try to send a test email
                $to = '<EMAIL>';
                $subject = 'Test Email from Contact Form';
                $message = 'This is a test email to verify form functionality.';
                $headers = 'From: <EMAIL>' . "\r\n" .
                          'Reply-To: <EMAIL>' . "\r\n" .
                          'X-Mailer: PHP/' . phpversion();
                
                if (function_exists('mail')) {
                    $mail_result = mail($to, $subject, $message, $headers);
                    $response['debug']['mail_sent'] = $mail_result;
                    
                    if ($mail_result) {
                        $response['success'] = true;
                        $response['message'] = 'Test email sent successfully';
                    } else {
                        $response['message'] = 'Failed to send test email';
                    }
                } else {
                    $response['message'] = 'Mail function not available';
                }
            } else {
                $response['message'] = 'Failed to decode form data';
            }
        } else {
            $response['message'] = 'No form data received';
        }
    } else {
        $response['message'] = 'No form type specified';
    }
} else {
    $response['message'] = 'Only POST requests allowed';
}

// Output response
echo json_encode($response, JSON_PRETTY_PRINT);
?>
