# Heart & Soul Website - Version 2 Upload Instructions

## FTP Upload Structure

### 📁 httpd.www (Public Folder)
Upload all contents of `httpd.www/` to your public web directory (usually `public_html/` or `www/`)

**Contents:**
- ✅ All HTML files (index.html, portfolio.html, etc.)
- ✅ CSS folder with all stylesheets
- ✅ JS folder with all JavaScript files
- ✅ IMG folder with all images
- ✅ Videos folder (if using self-hosted videos)
- ✅ Favicon and manifest files
- ✅ .htaccess for public configuration

### 📁 httpd.private (Private Folder)
Upload all contents of `httpd.private/` to a private directory outside your web root

**Contents:**
- ✅ Admin folder with dashboard and authentication
- ✅ PHP files for form processing
- ✅ .htaccess for private area security
- ✅ Database schema and configuration files

## 🚀 Upload Steps

1. **Connect to your FTP server**
2. **Upload httpd.www contents** to your public web directory
3. **Upload httpd.private contents** to a secure private directory
4. **Set proper file permissions:**
   - HTML/CSS/JS files: 644
   - PHP files: 644
   - Directories: 755
   - .htaccess files: 644

## 🔧 Configuration Required

### After Upload:
1. **Update .htaccess paths** in httpd.private/.htaccess
2. **Configure database connection** in PHP files
3. **Set up password protection** for admin area
4. **Test all forms and functionality**
5. **Verify Vimeo video embeds** are working

## 📋 Version 2 Features

### ✅ Updated Features:
- **Featured Videos**: 3 Vimeo-hosted videos with proper aspect ratios
- **Removed**: Showcase page and "Unsere Arbeiten" section
- **Updated Images**: Drohne.png for drone services, IMG_4336.JPG for livestream
- **Fixed**: Video player stretching issues
- **Grid Layout**: 2x3 responsive video grid
- **Minimal Controls**: Clean Vimeo player interface

### 🎯 Current Video Configuration:
1. **Video 1**: https://vimeo.com/1097571186/fd85174612
2. **Video 2**: https://vimeo.com/1097571254/7a2af31444
3. **Video 3**: https://vimeo.com/1097577102/78c87494a0

## 🔒 Security Notes

- Private folder is protected with .htaccess
- Admin area requires authentication
- Sensitive files are blocked from public access
- HTTPS redirection enabled
- Security headers implemented

## 📞 Support

For technical support or questions about this upload, contact the development team.

---
**Heart & Soul Film- und Medienproduktion**  
Version 2 - Ready for Production
