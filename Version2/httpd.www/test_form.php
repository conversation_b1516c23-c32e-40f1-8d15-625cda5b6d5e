<?php
/**
 * Form Test Script
 * 
 * This script tests the form functionality and email sending
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Configuration
$recipient_email = '<EMAIL>';

echo "<h1>Form Test Script</h1>";
echo "<p>Testing form functionality...</p>";

// Test 1: Check if mail function exists
echo "<h2>Test 1: Mail Function</h2>";
if (function_exists('mail')) {
    echo "<p style='color: green;'>✓ Mail function is available</p>";
} else {
    echo "<p style='color: red;'>✗ Mail function is NOT available</p>";
}

// Test 2: Check if we can send a simple email
echo "<h2>Test 2: Simple Email Test</h2>";
$test_subject = 'Test Email from Heart & Soul Website';
$test_message = 'This is a test email to verify that the form system is working correctly.';
$test_headers = 'From: <EMAIL>' . "\r\n" .
               'Reply-To: <EMAIL>' . "\r\n" .
               'X-Mailer: PHP/' . phpversion();

$mail_result = mail($recipient_email, $test_subject, $test_message, $test_headers);

if ($mail_result) {
    echo "<p style='color: green;'>✓ Test email sent successfully to $recipient_email</p>";
} else {
    echo "<p style='color: red;'>✗ Failed to send test email</p>";
}

// Test 3: Check POST data handling
echo "<h2>Test 3: POST Data Handling</h2>";
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<p style='color: blue;'>POST request received</p>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    if (isset($_POST['test_form'])) {
        $form_data = [
            'name' => $_POST['name'] ?? 'Test User',
            'email' => $_POST['email'] ?? '<EMAIL>',
            'message' => $_POST['message'] ?? 'Test message'
        ];
        
        $subject = 'Test Form Submission';
        $message = "Test form submission:\n\n";
        $message .= "Name: " . $form_data['name'] . "\n";
        $message .= "Email: " . $form_data['email'] . "\n";
        $message .= "Message: " . $form_data['message'] . "\n";
        
        $headers = 'From: <EMAIL>' . "\r\n" .
                  'Reply-To: ' . $form_data['email'] . "\r\n" .
                  'X-Mailer: PHP/' . phpversion();
        
        $result = mail($recipient_email, $subject, $message, $headers);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Form test email sent successfully</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to send form test email</p>";
        }
    }
} else {
    echo "<p>No POST data received. Use the form below to test:</p>";
}

// Test 4: Check server configuration
echo "<h2>Test 4: Server Configuration</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";

// Check sendmail configuration
$sendmail_path = ini_get('sendmail_path');
echo "<p><strong>Sendmail Path:</strong> " . ($sendmail_path ?: 'Not configured') . "</p>";

$smtp = ini_get('SMTP');
echo "<p><strong>SMTP Server:</strong> " . ($smtp ?: 'Not configured') . "</p>";

$smtp_port = ini_get('smtp_port');
echo "<p><strong>SMTP Port:</strong> " . ($smtp_port ?: 'Not configured') . "</p>";

?>

<h2>Test Form</h2>
<form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>">
    <input type="hidden" name="test_form" value="1">
    
    <p>
        <label for="name">Name:</label><br>
        <input type="text" id="name" name="name" value="Test User" required>
    </p>
    
    <p>
        <label for="email">Email:</label><br>
        <input type="email" id="email" name="email" value="<EMAIL>" required>
    </p>
    
    <p>
        <label for="message">Message:</label><br>
        <textarea id="message" name="message" rows="4" required>This is a test message from the form test script.</textarea>
    </p>
    
    <p>
        <button type="submit">Send Test Email</button>
    </p>
</form>

<h2>Test send_form.php</h2>
<p>Click the button below to test the actual form handler:</p>
<button onclick="testFormHandler()">Test Form Handler</button>

<div id="test-result"></div>

<script>
function testFormHandler() {
    const testData = {
        name: 'Test User',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: 'This is a test message from the form handler test.',
        company: 'Test Company',
        phone: '+49 **********'
    };
    
    const formData = new FormData();
    formData.append('form_type', 'contact');
    formData.append('form_data', JSON.stringify(testData));
    formData.append('website', ''); // Honeypot
    
    fetch('/send_form.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('test-result');
        if (data.success) {
            resultDiv.innerHTML = '<p style="color: green;">✓ Form handler test successful: ' + data.message + '</p>';
        } else {
            resultDiv.innerHTML = '<p style="color: red;">✗ Form handler test failed: ' + data.message + '</p>';
        }
    })
    .catch(error => {
        const resultDiv = document.getElementById('test-result');
        resultDiv.innerHTML = '<p style="color: red;">✗ Error testing form handler: ' + error.message + '</p>';
    });
}
</script>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

form {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    margin: 20px 0;
}

input, textarea {
    width: 100%;
    padding: 8px;
    margin: 5px 0;
    border: 1px solid #ddd;
    border-radius: 3px;
}

button {
    background: #ff3030;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

button:hover {
    background: #e02020;
}

pre {
    background: #f0f0f0;
    padding: 10px;
    border-radius: 3px;
    overflow-x: auto;
}
</style>
