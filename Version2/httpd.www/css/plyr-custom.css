/* Custom Plyr styling - Change UI from blue to red */

:root {
  --plyr-color-main: #ff3030; /* Primary red color matching the website theme */
  --plyr-range-thumb-background: #ff3030;
  --plyr-range-fill-background: #ff3030;
  --plyr-control-icon-size: 18px;
  --plyr-audio-controls-background: rgba(18, 18, 18, 0.85);
  --plyr-audio-control-color: #fff;
  --plyr-audio-control-color-hover: #ff3030;
  --plyr-badge-background: #ff3030;
  --plyr-badge-text-color: #fff;
  --plyr-control-radius: 4px;
  --plyr-control-toggle-checked-background: #ff3030;
  --plyr-video-controls-background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.75));
  --plyr-video-control-color: #fff;
  --plyr-video-control-color-hover: #ff3030;
  --plyr-menu-background: rgba(18, 18, 18, 0.9);
  --plyr-menu-color: #fff;
  --plyr-menu-item-arrow-color: #fff;
  --plyr-menu-item-arrow-color-hover: #ff3030;
  --plyr-menu-border-color: rgba(255, 255, 255, 0.15);
  --plyr-menu-radius: 4px;
  --plyr-tooltip-background: rgba(18, 18, 18, 0.9);
  --plyr-tooltip-color: #fff;
  --plyr-tooltip-radius: 4px;
  --plyr-progress-loading-background: rgba(255, 255, 255, 0.2);
  --plyr-progress-loading-size: 25px;
}

/* Custom styling for the play button */
.plyr__control--overlaid {
  background: rgba(255, 48, 48, 0.8) !important;
}

.plyr__control--overlaid:hover {
  background: rgba(255, 48, 48, 1) !important;
}

/* Custom styling for the progress bar */
.plyr--full-ui input[type=range] {
  color: #ff3030 !important;
}

/* Custom styling for the volume bar */
.plyr--full-ui input[type=range].plyr__tab-focus::-webkit-slider-runnable-track {
  box-shadow: 0 0 0 2px rgba(255, 48, 48, 0.25) !important;
}

.plyr--full-ui input[type=range].plyr__tab-focus::-moz-range-track {
  box-shadow: 0 0 0 2px rgba(255, 48, 48, 0.25) !important;
}

.plyr--full-ui input[type=range].plyr__tab-focus::-ms-track {
  box-shadow: 0 0 0 2px rgba(255, 48, 48, 0.25) !important;
}

/* Custom styling for the control buttons */
.plyr__control.plyr__tab-focus {
  box-shadow: 0 0 0 2px rgba(255, 48, 48, 0.25) !important;
}

.plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]::before {
  background: #ff3030 !important;
}

/* Custom styling for the time display */
.plyr__time {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Custom styling for the video container */
.plyr {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Custom loading animation color */
.plyr__control--overlaid .plyr__control__icon {
  color: #fff;
}

/* Custom styling for the video player container */
.video-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}
