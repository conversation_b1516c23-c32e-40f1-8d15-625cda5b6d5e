/* Leistungen Page Styles */

/* Intro Section */
.leistungen-intro {
  padding: 5rem 0 3rem;
}

.leistungen-intro-content {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 1.5rem;
  text-align: center;
}

.leistungen-intro-content h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: var(--text-light);
  position: relative;
  padding-bottom: 1rem;
  display: inline-block;
}

.leistungen-intro-content h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 1.5px;
}

.leistungen-intro-content p {
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--text-gray);
  margin-bottom: 1.5rem;
}

/* Grid Section */
.leistungen-grid {
  padding: 3rem 0 5rem;
}

.leistungen-grid-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2.5rem;
}

/* Card Styles */
.leistungen-card {
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 30pt;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.leistungen-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 48, 48, 0.1);
}

.leistungen-card-image {
  height: 250px;
  background-size: cover;
  background-position: center;
  border-radius: 15pt;
  margin: 15px;
}

.leistungen-card-content {
  padding: 0 2rem 2rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.leistungen-card-content h3 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.leistungen-card-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-gray);
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.leistungen-card-content .btn {
  align-self: flex-start;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .leistungen-grid-container {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 992px) {
  .leistungen-intro {
    padding: 4rem 0 2rem;
  }
  
  .leistungen-intro-content h2 {
    font-size: 2.2rem;
  }
  
  .leistungen-intro-content p {
    font-size: 1.1rem;
  }
  
  .leistungen-grid {
    padding: 2rem 0 4rem;
  }
  
  .leistungen-grid-container {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .leistungen-intro {
    padding: 3rem 0 1.5rem;
  }
  
  .leistungen-intro-content h2 {
    font-size: 1.8rem;
  }
  
  .leistungen-grid-container {
    grid-template-columns: 1fr;
    max-width: 500px;
  }
  
  .leistungen-card-image {
    height: 200px;
  }
  
  .leistungen-card-content h3 {
    font-size: 1.6rem;
  }
}
