/* Portfolio Page Styles */

.portfolio-section {
  padding: 5rem 0;
}

.portfolio-intro {
  max-width: 900px;
  margin: 0 auto 3rem;
  text-align: center;
}

.portfolio-intro p {
  font-size: 1.3rem;
  line-height: 1.7;
  color: var(--text-light);
}



/* Portfolio Grid */
.portfolio-grid {
  /* Fallback for older browsers */
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;

  /* Grid for modern browsers */
  display: -webkit-grid;
  display: -ms-grid;
  display: grid;
  -webkit-grid-template-columns: repeat(2, 1fr);
  -ms-grid-columns: 1fr 2.5rem 1fr;
  grid-template-columns: repeat(2, 1fr);
  -webkit-gap: 2.5rem;
  -moz-gap: 2.5rem;
  gap: 2.5rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Flexbox fallback for portfolio items */
.portfolio-item {
  /* Flexbox fallback */
  -webkit-flex: 0 0 calc(50% - 1.25rem);
  -ms-flex: 0 0 calc(50% - 1.25rem);
  flex: 0 0 calc(50% - 1.25rem);
  margin-bottom: 2.5rem;
}

.portfolio-item {
  background-color: var(--dark-secondary);
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  overflow: hidden;
  -webkit-transition: -webkit-transform 0.3s ease, box-shadow 0.3s ease;
  -moz-transition: -moz-transform 0.3s ease, box-shadow 0.3s ease;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  /* Flexbox fallback */
  -webkit-flex: 0 0 calc(50% - 1.25rem);
  -ms-flex: 0 0 calc(50% - 1.25rem);
  flex: 0 0 calc(50% - 1.25rem);
  margin-bottom: 2.5rem;
}

.portfolio-item:hover {
  -webkit-transform: translateY(-10px);
  -moz-transform: translateY(-10px);
  -ms-transform: translateY(-10px);
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

/* Ensure proper video container sizing */
.portfolio-video {
  width: 100%;
  border-radius: 15px;
  overflow: hidden;
}

.vimeo-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
}

.vimeo-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  border-radius: 15px;
}



/* Responsive Styles */
@media (max-width: 1200px) {
  .portfolio-grid {
    gap: 2rem;
  }
}

@media (max-width: 992px) {
  .portfolio-section {
    padding: 4rem 2rem;
  }

  .portfolio-grid {
    gap: 1.5rem;
  }

  .portfolio-intro p {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .portfolio-section {
    padding: 3rem 1.5rem;
  }

  .portfolio-grid {
    /* Flexbox fallback for mobile */
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;

    /* Grid for modern browsers */
    -webkit-grid-template-columns: 1fr;
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    max-width: 500px;
    -webkit-gap: 2rem;
    -moz-gap: 2rem;
    gap: 2rem;
    padding: 0 1rem;
  }

  .portfolio-item {
    /* Full width on mobile */
    -webkit-flex: 0 0 100%;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* Desktop specific styles */
@media (min-width: 769px) {
  .portfolio-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .vimeo-container {
    min-height: 300px;
  }
}

/* Plyr Video Player Customization for Portfolio */
.portfolio-video .plyr {
  border-radius: 0;
}

.portfolio-video .plyr--video {
  background-color: var(--dark-bg);
}

/* Video Poster Image */
.portfolio-video video {
  width: 100%;
  height: auto;
  display: block;
}
