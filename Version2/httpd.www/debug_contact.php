<?php
/**
 * Debug Contact Form
 * Simple test to see what's happening with form submissions
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Contact Form Debug</h1>";
echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>POST Request Received</h2>";
    echo "<h3>POST Data:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    echo "<h3>FILES Data:</h3>";
    echo "<pre>";
    print_r($_FILES);
    echo "</pre>";
    
    // Test email sending
    $to = '<EMAIL>';
    $subject = 'Test Email from Debug Script';
    $message = 'This is a test email sent at ' . date('Y-m-d H:i:s');
    $headers = 'From: <EMAIL>';
    
    echo "<h3>Email Test:</h3>";
    $result = mail($to, $subject, $message, $headers);
    if ($result) {
        echo "<p style='color: green;'>✓ Test email sent successfully to $to</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to send test email</p>";
    }
    
    // Process form data if present
    if (isset($_POST['name']) && isset($_POST['email'])) {
        $name = htmlspecialchars(trim($_POST['name']));
        $email = htmlspecialchars(trim($_POST['email']));
        $subject_form = htmlspecialchars(trim($_POST['subject'] ?? 'No Subject'));
        $message_form = htmlspecialchars(trim($_POST['message'] ?? 'No Message'));
        
        echo "<h3>Form Processing:</h3>";
        echo "<p><strong>Name:</strong> $name</p>";
        echo "<p><strong>Email:</strong> $email</p>";
        echo "<p><strong>Subject:</strong> $subject_form</p>";
        echo "<p><strong>Message:</strong> $message_form</p>";
        
        // Try to send the actual form email
        $email_subject = 'Contact Form Submission from ' . $name;
        $email_body = "Name: $name\nEmail: $email\nSubject: $subject_form\nMessage: $message_form";
        $email_headers = "From: <EMAIL>\nReply-To: $email";
        
        $form_result = mail($to, $email_subject, $email_body, $email_headers);
        if ($form_result) {
            echo "<p style='color: green;'>✓ Form email sent successfully</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to send form email</p>";
        }
    }
    
} else {
    echo "<h2>No POST Request</h2>";
    echo "<p>This script only processes POST requests.</p>";
}

// Show server info
echo "<h2>Server Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";

// Check mail configuration
echo "<h3>Mail Configuration:</h3>";
$sendmail_path = ini_get('sendmail_path');
echo "<p><strong>Sendmail Path:</strong> " . ($sendmail_path ?: 'Not configured') . "</p>";

$smtp = ini_get('SMTP');
echo "<p><strong>SMTP Server:</strong> " . ($smtp ?: 'Not configured') . "</p>";

$smtp_port = ini_get('smtp_port');
echo "<p><strong>SMTP Port:</strong> " . ($smtp_port ?: 'Not configured') . "</p>";

?>

<h2>Test Form</h2>
<form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>" style="max-width: 500px; margin: 20px 0;">
    <p>
        <label for="name">Name:</label><br>
        <input type="text" id="name" name="name" value="Test User" required style="width: 100%; padding: 8px;">
    </p>
    
    <p>
        <label for="email">Email:</label><br>
        <input type="email" id="email" name="email" value="<EMAIL>" required style="width: 100%; padding: 8px;">
    </p>
    
    <p>
        <label for="subject">Subject:</label><br>
        <input type="text" id="subject" name="subject" value="Test Subject" required style="width: 100%; padding: 8px;">
    </p>
    
    <p>
        <label for="message">Message:</label><br>
        <textarea id="message" name="message" rows="4" required style="width: 100%; padding: 8px;">This is a test message from the debug script.</textarea>
    </p>
    
    <p>
        <button type="submit" style="background: #ff3030; color: white; padding: 10px 20px; border: none; border-radius: 3px;">Send Test</button>
    </p>
</form>

<h2>Test AJAX</h2>
<button onclick="testAjax()" style="background: #ff3030; color: white; padding: 10px 20px; border: none; border-radius: 3px;">Test AJAX Request</button>
<div id="ajax-result" style="margin-top: 10px; padding: 10px; background: #f0f0f0; border-radius: 3px;"></div>

<script>
function testAjax() {
    const formData = new FormData();
    formData.append('name', 'AJAX Test User');
    formData.append('email', '<EMAIL>');
    formData.append('subject', 'AJAX Test Subject');
    formData.append('message', 'This is an AJAX test message.');
    formData.append('ajax', '1');
    
    fetch('/simple_contact.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('ajax-result').innerHTML = '<strong>AJAX Response:</strong><br>' + data;
    })
    .catch(error => {
        document.getElementById('ajax-result').innerHTML = '<strong>AJAX Error:</strong><br>' + error.message;
    });
}
</script>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

pre {
    background: #f0f0f0;
    padding: 10px;
    border-radius: 3px;
    overflow-x: auto;
}

h1, h2, h3 {
    color: #333;
}
</style>
