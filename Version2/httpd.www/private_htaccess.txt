# .htaccess für httpd.private Ordner
# Diese Datei als .htaccess in httpd.private/ speichern

# Deny all web access to this directory
Order deny,allow
Deny from all

# Prevent directory listing
Options -Indexes

# Protect sensitive files
<FilesMatch "\.(php|sql|log|md|txt|sh|json)$">
    Order deny,allow
    Deny from all
</FilesMatch>

# Additional security headers
<IfModule mod_headers.c>
    Header always set X-Robots-Tag "noindex, nofollow, nosnippet, noarchive"
</IfModule>
