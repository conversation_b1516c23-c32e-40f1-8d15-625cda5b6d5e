<?php
/**
 * Form Submission Handler
 *
 * This script processes form submissions and sends them via <NAME_EMAIL>
 * It includes security measures to prevent common vulnerabilities.
 */

// Set strict error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1); // Enable for debugging
ini_set('log_errors', 1);

// Log all requests for debugging
error_log('Form submission received: ' . $_SERVER['REQUEST_METHOD'] . ' ' . $_SERVER['REQUEST_URI']);

// Configuration
$recipient_email = '<EMAIL>';
$site_name = 'Heart & Soul Medienproduktion';
$max_email_length = 1000000; // Prevent extremely large emails

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Content-Security-Policy: default-src \'self\'');

/**
 * Sanitize input to prevent XSS and other attacks
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Check for header injection attempts
 */
function has_header_injection($str) {
    return preg_match('/[\r\n]/', $str);
}

/**
 * Validate email address
 */
function is_valid_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Check honeypot field (should be empty)
 */
function check_honeypot() {
    return empty($_POST['website']);
}

/**
 * Send secure email
 */
function send_secure_email($to, $subject, $message, $from_email, $from_name) {
    // Validate email addresses
    if (!is_valid_email($to) || !is_valid_email($from_email)) {
        return false;
    }
    
    // Check for header injection
    if (has_header_injection($to) || has_header_injection($subject) || 
        has_header_injection($from_email) || has_header_injection($from_name)) {
        return false;
    }
    
    // Prepare headers
    $headers = "From: " . $from_name . " <" . $from_email . ">\r\n";
    $headers .= "Reply-To: " . $from_email . "\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    // Limit message size
    if (strlen($message) > $GLOBALS['max_email_length']) {
        $message = substr($message, 0, $GLOBALS['max_email_length']) . '... [Content truncated due to size]';
    }
    
    // Send email
    return mail($to, $subject, $message, $headers);
}

// Initialize response array
$response = [
    'success' => false,
    'message' => 'An error occurred'
];

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log('POST data received: ' . print_r($_POST, true));

    // Check honeypot
    if (!check_honeypot()) {
        error_log('Honeypot triggered - possible bot detected');
        // Don't reveal that we detected a bot
        $response['success'] = true;
        $response['message'] = 'Form processed successfully';
        echo json_encode($response);
        exit;
    }
    
    // Validate required fields
    $required_fields = ['form_type', 'form_data'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            error_log('Missing required field: ' . $field);
            $response['message'] = 'Missing required fields: ' . $field;
            echo json_encode($response);
            exit;
        }
    }

    error_log('All required fields present');
    
    // Sanitize inputs
    $form_type = sanitize_input($_POST['form_type']);
    $form_data = json_decode($_POST['form_data'], true);
    
    // Validate form data
    if (!is_array($form_data)) {
        $response['message'] = 'Invalid form data format';
        echo json_encode($response);
        exit;
    }
    
    // Sanitize all form data fields
    foreach ($form_data as $key => $value) {
        if (is_string($value)) {
            $form_data[$key] = sanitize_input($value);
        }
    }
    
    // Prepare email content based on form type
    $subject = '';
    $message = '';
    $from_email = '<EMAIL>';
    $from_name = 'Heart & Soul Medienproduktion Website';
    
    if ($form_type === 'meeting') {
        $subject = 'Neue Terminanfrage von ' . ($form_data['name'] ?? 'Unbekannt');
        $message = '<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Neue Terminanfrage</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #ff3030;">Neue Terminanfrage</h2>
                
                <div style="background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0;">
                    <h3>Kontaktdaten:</h3>
                    <p><strong>Name:</strong> ' . ($form_data['name'] ?? 'Nicht angegeben') . '</p>
                    <p><strong>Firma:</strong> ' . ($form_data['company'] ?? 'Nicht angegeben') . '</p>
                    <p><strong>E-Mail:</strong> ' . ($form_data['email'] ?? 'Nicht angegeben') . '</p>
                    <p><strong>Telefon:</strong> ' . ($form_data['phone'] ?? 'Nicht angegeben') . '</p>
                </div>
                
                <div style="background-color: #f0f8ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
                    <h3>Gewünschter Termin:</h3>
                    <p><strong>Datum:</strong> ' . ($form_data['date'] ?? 'Nicht angegeben') . '</p>
                    <p><strong>Uhrzeit:</strong> ' . ($form_data['time'] ?? 'Nicht angegeben') . '</p>
                    <p><strong>Eingegangen am:</strong> ' . date('d.m.Y H:i:s') . '</p>
                </div>
                
                <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 20px;">
                    <p>Diese E-Mail wurde automatisch vom Heart & Soul Medienproduktion Website-Formular gesendet.</p>
                </div>
            </div>
        </body>
        </html>';
    } 
    elseif ($form_type === 'contact') {
        $subject = 'Neue Kontaktanfrage von ' . ($form_data['name'] ?? 'Unbekannt');
        $message = '<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Neue Kontaktanfrage</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #ff3030;">Neue Kontaktanfrage</h2>
                
                <div style="background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0;">
                    <h3>Kontaktdaten:</h3>
                    <p><strong>Name:</strong> ' . ($form_data['name'] ?? 'Nicht angegeben') . '</p>
                    <p><strong>Firma:</strong> ' . ($form_data['company'] ?? 'Nicht angegeben') . '</p>
                    <p><strong>E-Mail:</strong> ' . ($form_data['email'] ?? 'Nicht angegeben') . '</p>
                    <p><strong>Telefon:</strong> ' . ($form_data['phone'] ?? 'Nicht angegeben') . '</p>
                </div>
                
                <div style="margin-top: 30px;">
                    <h3>Anfrage:</h3>
                    <p><strong>Betreff:</strong> ' . ($form_data['subject'] ?? 'Kein Betreff') . '</p>
                    <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 10px;">
                        <p>' . nl2br($form_data['message'] ?? 'Keine Nachricht') . '</p>
                    </div>
                    <p><strong>Eingegangen am:</strong> ' . date('d.m.Y H:i:s') . '</p>
                </div>
                
                <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 20px;">
                    <p>Diese E-Mail wurde automatisch vom Heart & Soul Medienproduktion Website-Formular gesendet.</p>
                </div>
            </div>
        </body>
        </html>';
    }
    else {
        $response['message'] = 'Invalid form type';
        echo json_encode($response);
        exit;
    }
    
    // Send email
    error_log('Attempting to send email to: ' . $recipient_email);
    error_log('Email subject: ' . $subject);

    $email_sent = send_secure_email(
        $recipient_email,
        $subject,
        $message,
        $from_email,
        $from_name
    );

    if ($email_sent) {
        error_log('Email sent successfully');
        $response['success'] = true;
        $response['message'] = 'Email sent successfully';
    } else {
        error_log('Failed to send email to ' . $recipient_email);
        $response['message'] = 'Failed to send email';
    }
} else {
    $response['message'] = 'Only POST requests allowed';
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
?>
