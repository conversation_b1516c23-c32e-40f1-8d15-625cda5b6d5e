document.addEventListener('DOMContentLoaded', function() {
  console.log('Portfolio.js loaded');

  // Check if Vimeo iframes exist
  var vimeoIframes = document.querySelectorAll('.vimeo-container iframe');
  console.log('Found', vimeoIframes.length, 'Vimeo videos');

  // Check if IntersectionObserver is supported (Safari 12.1+)
  if ('IntersectionObserver' in window) {
    // Simple intersection observer for lazy loading Vimeo videos
    var vimeoObserver = new IntersectionObserver(function(entries) {
      for (var i = 0; i < entries.length; i++) {
        var entry = entries[i];
        if (entry.isIntersecting) {
          var iframe = entry.target;
          console.log('Vimeo video entering viewport:', iframe.src);

          // Vimeo iframes are already loaded, just log for debugging
          if (!iframe.dataset.observed) {
            iframe.dataset.observed = 'true';
            console.log('Vimeo iframe initialized:', iframe.title);
          }

          // Stop observing this iframe after first intersection
          vimeoObserver.unobserve(iframe);
        }
      }
    }, {
      rootMargin: '50px' // Start loading 50px before video enters viewport
    });

    // Observe all Vimeo iframes
    for (var j = 0; j < vimeoIframes.length; j++) {
      vimeoObserver.observe(vimeoIframes[j]);
    }
  } else {
    // Fallback for older browsers
    console.log('IntersectionObserver not supported, initializing all videos immediately');
    for (var k = 0; k < vimeoIframes.length; k++) {
      var iframe = vimeoIframes[k];
      iframe.dataset.observed = 'true';
      console.log('Vimeo iframe initialized (fallback):', iframe.title);
    }
  }

  // Handle any legacy video elements if they exist
  var portfolioVideos = document.querySelectorAll('.portfolio-video video');
  if (portfolioVideos.length > 0) {
    console.log('Found', portfolioVideos.length, 'legacy video elements');

    for (var l = 0; l < portfolioVideos.length; l++) {
      var video = portfolioVideos[l];
      var source = video.querySelector('source');
      console.log('Legacy video ' + (l + 1) + ':', source ? source.src : 'no source');
    }
  }

  // Portfolio grid responsive behavior
  function handlePortfolioResize() {
    var portfolioGrid = document.querySelector('.portfolio-grid');
    if (portfolioGrid) {
      var items = portfolioGrid.querySelectorAll('.portfolio-item');
      console.log('Portfolio grid contains', items.length, 'items');
    }
  }

  // Call on load and resize
  handlePortfolioResize();
  window.addEventListener('resize', handlePortfolioResize);
});
