document.addEventListener('DOMContentLoaded', function() {
  // Ensure hero video plays properly
  var heroVideo = document.querySelector('.hero-video');
  if (heroVideo) {
    // Force load and play the hero video
    heroVideo.load();

    // Make sure autoplay works
    var playPromise = heroVideo.play();
    if (playPromise !== undefined) {
      playPromise.catch(function(error) {
        console.log('Hero video autoplay failed:', error);
        // Add a play button if autoplay fails
        var heroSection = document.querySelector('.hero-video-section');
        var playButton = document.createElement('button');
        playButton.innerHTML = '<i class="fas fa-play"></i>';
        playButton.className = 'hero-play-button';
        playButton.addEventListener('click', function() {
          heroVideo.play();
          playButton.style.display = 'none';
        });
        heroSection.appendChild(playButton);
      });
    }
  }

  // Initialize Plyr for featured videos (not on portfolio page)
  var isPortfolioPage = window.location.pathname.indexOf('portfolio.html') !== -1;

  if (!isPortfolioPage) {
    // Check if Plyr is available
    if (typeof Plyr === 'undefined') {
      console.error('Plyr is not loaded!');
      return;
    }

    console.log('Plyr is available, initializing players...');

    // Wait for videos to be ready
    setTimeout(function() {
      var featuredVideos = document.querySelectorAll('.featured-video');
      console.log('Found', featuredVideos.length, 'featured videos');

      if (featuredVideos.length === 0) {
        console.log('No featured videos found, trying all videos except hero');
        var allVideos = document.querySelectorAll('video:not(.hero-video)');
        console.log('Found', allVideos.length, 'non-hero videos');

        for (var i = 0; i < allVideos.length; i++) {
          var video = allVideos[i];
          var source = video.querySelector('source');
          console.log('Video ' + (i + 1) + ':', source ? source.src : 'no source');

          try {
            var player = new Plyr(video, {
              controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
              autoplay: false,
              autopause: true
            });

            player.on('ready', function() {
              console.log('✅ Player ' + (i + 1) + ' ready');
            });

            player.on('error', function(error) {
              console.error('❌ Player ' + (i + 1) + ' error:', error);
            });

          } catch (error) {
            console.error('Failed to initialize player ' + (i + 1) + ':', error);
          }
        }
      } else {
        // Initialize featured videos
        for (var j = 0; j < featuredVideos.length; j++) {
          var video = featuredVideos[j];
          var source = video.querySelector('source');
          console.log('Initializing featured video ' + (j + 1) + ':', source ? source.src : 'no source');

          try {
            var player = new Plyr(video, {
              controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
              autoplay: false,
              autopause: true
            });

            player.on('ready', function() {
              console.log('✅ Featured player ' + (j + 1) + ' ready');
            });

            player.on('error', function(error) {
              console.error('❌ Featured player ' + (j + 1) + ' error:', error);
            });

          } catch (error) {
            console.error('Failed to initialize featured player ' + (j + 1) + ':', error);
          }
        }
      }
    }, 1000);
  }

  // Transparente Navigation beim Scrollen
  var navbar = document.querySelector('.navbar');
  var mobileMenuBtn = document.querySelector('.mobile-menu-btn');
  var mobileMenu = document.querySelector('.mobile-menu');

  // Scroll-Event für die Navigation
  window.addEventListener('scroll', function() {
    if (window.scrollY > 50) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });

  // Mobile Menü Toggle
  if (mobileMenuBtn) {
    mobileMenuBtn.addEventListener('click', function() {
      mobileMenu.classList.toggle('active');
    });
  }

  // Schließe das mobile Menü, wenn ein Link geklickt wird
  var mobileLinks = document.querySelectorAll('.mobile-menu a');
  for (var k = 0; k < mobileLinks.length; k++) {
    mobileLinks[k].addEventListener('click', function() {
      mobileMenu.classList.remove('active');
    });
  }

  // Handle active state for navigation links
  const navLinks = document.querySelectorAll('.nav-links a');
  navLinks.forEach(link => {
    link.addEventListener('click', function() {
      // Remove active class from all links
      navLinks.forEach(l => l.classList.remove('active'));
      // Add active class to clicked link
      this.classList.add('active');

      // Update mobile menu active state as well
      const href = this.getAttribute('href');
      const mobileLink = document.querySelector(`.mobile-menu a[href="${href}"]`);
      if (mobileLink) {
        document.querySelectorAll('.mobile-menu a').forEach(l => l.classList.remove('active'));
        mobileLink.classList.add('active');
      }
    });
  });



  // Smooth Scroll für Anker-Links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      e.preventDefault();
      const targetId = this.getAttribute('href');
      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        window.scrollTo({
          top: targetElement.offsetTop - 80, // Offset für die Navigation
          behavior: 'smooth'
        });
      }
    });
  });

  // Einfaches Formular-Handling
  const contactForm = document.getElementById('contact-form');
  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();
      // Hier könnte später eine echte Formularverarbeitung implementiert werden
      alert('Vielen Dank für Ihre Nachricht! Wir werden uns in Kürze bei Ihnen melden.');
      contactForm.reset();
    });
  }

  // Animationen beim Scrollen - Früher triggern für bessere UX
  const animateOnScroll = function() {
    const elements = document.querySelectorAll('.product-card, .video-item, .section-title, .welcome-content, .welcome-features');

    elements.forEach(element => {
      const elementPosition = element.getBoundingClientRect().top;
      const elementBottom = element.getBoundingClientRect().bottom;
      const windowHeight = window.innerHeight;

      // Animation triggert sehr früh - Element ist noch 300px unter dem Viewport
      if (elementPosition < windowHeight + 300 && elementBottom > -100) {
        element.classList.add('animated');
      }
    });
  };

  // Sofortige Animation für Welcome-Sektion beim Laden
  const welcomeContent = document.querySelector('.welcome-content');
  const welcomeFeatures = document.querySelector('.welcome-features');

  if (welcomeContent) {
    setTimeout(() => {
      welcomeContent.classList.add('animated');
    }, 300);
  }

  if (welcomeFeatures) {
    setTimeout(() => {
      welcomeFeatures.classList.add('animated');
    }, 600);
  }

  // Initialisiere Animationen
  window.addEventListener('scroll', animateOnScroll);
  animateOnScroll(); // Führe beim Laden der Seite aus
});