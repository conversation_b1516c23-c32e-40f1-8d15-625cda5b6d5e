document.addEventListener('DOMContentLoaded', function() {
  var contactForm = document.getElementById('contact-form');

  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // Get form values
      var name = document.getElementById('name').value.trim();
      var company = document.getElementById('company').value.trim();
      var email = document.getElementById('email').value.trim();
      var phone = document.getElementById('phone').value.trim();
      var subject = document.getElementById('subject').value.trim();
      var message = document.getElementById('message').value.trim();
      var privacy = document.getElementById('privacy').checked;

      // Simple validation
      if (!name || !email || !subject || !message || !privacy) {
        alert('Bitte füllen Sie alle Pflichtfelder aus.');
        return;
      }

      // Email validation
      var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        alert('Bitte geben Sie eine gültige E-Mail-Adresse ein.');
        return;
      }

      // Disable the submit button and show loading state
      var submitButton = contactForm.querySelector('button[type="submit"]');
      var originalButtonHTML = submitButton.innerHTML;
      submitButton.disabled = true;
      submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Wird gesendet...';

      // Prepare form data
      var formData = new FormData();
      formData.append('name', name);
      formData.append('company', company);
      formData.append('email', email);
      formData.append('phone', phone);
      formData.append('subject', subject);
      formData.append('message', message);
      formData.append('privacy', privacy ? '1' : '');
      formData.append('ajax', '1'); // Mark as AJAX request

      // Send the data to the server
      fetch('/simple_contact.php', {
        method: 'POST',
        body: formData
      })
      .then(function(response) {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(function(data) {
        if (data.success) {
          // Show success message
          alert('✓ Nachricht erfolgreich gesendet!\n\nVielen Dank für Ihre Nachricht. Wir werden uns schnellstmöglich bei Ihnen melden.');

          // Reset form
          contactForm.reset();

          // Optional: Redirect to thank you page or show inline success
          // window.location.href = 'danke.html';
        } else {
          throw new Error(data.message || 'Unbekannter Fehler beim Senden');
        }
      })
      .catch(function(error) {
        console.error('Error:', error);

        // Show error message
        alert('✗ Fehler beim Senden: ' + error.message + '\n\nBitte versuchen Sie es erneut oder nutzen Sie den direkten E-Mail-Link unten.');
      })
      .finally(function() {
        // Re-enable the submit button
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonHTML;
      });
    });
  }
});
