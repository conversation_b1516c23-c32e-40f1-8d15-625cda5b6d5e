document.addEventListener('DOMContentLoaded', function() {
  // Initialize Locomotive Scroll with minimal settings
  const scroll = new LocomotiveScroll({
    el: document.querySelector('[data-scroll-container]'),
    smooth: true,
    smoothMobile: false,
    inertia: 0.5,
    getDirection: true,
    reloadOnContextChange: true,
    lerp: 0.08,
    multiplier: 1.0,
    smartphone: {
      smooth: false
    },
    tablet: {
      smooth: true,
      breakpoint: 1024
    }
  });

  // Fix for hero video - ensure it's properly loaded
  const heroVideo = document.querySelector('.hero-video');
  if (heroVideo) {
    heroVideo.addEventListener('loadeddata', () => {
      scroll.update();
    });
  }

  // Update scroll on window resize
  window.addEventListener('resize', () => {
    setTimeout(() => {
      scroll.update();
    }, 500);
  });

  // Update scroll when images and other resources are loaded
  window.addEventListener('load', () => {
    scroll.update();
  });

  // Handle navigation links
  const navLinks = document.querySelectorAll('.nav-links a, .mobile-menu a');
  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const targetId = this.getAttribute('href');

      // If it's a hash link, scroll to the section
      if (targetId.startsWith('#')) {
        const targetSection = document.querySelector(targetId);
        if (targetSection) {
          scroll.scrollTo(targetSection);

          // Close mobile menu if open
          const mobileMenu = document.querySelector('.mobile-menu');
          if (mobileMenu && mobileMenu.classList.contains('active')) {
            mobileMenu.classList.remove('active');
          }
        }
      } else {
        // If it's a regular link, navigate to the page
        window.location.href = targetId;
      }
    });
  });

  // Add animation classes to elements
  function initAnimations() {
    // Add fade-in class to section titles
    document.querySelectorAll('.section-title, .page-title, .blog-title').forEach(el => {
      el.classList.add('fade-in');
      el.setAttribute('data-scroll', '');
      el.setAttribute('data-scroll-offset', '30%');
    });

    // Add fade-in class to section subtitles and descriptions
    document.querySelectorAll('.page-subtitle, .section-subtitle, .welcome-subtitle, .blog-description, .cta-text, .cta-subheadline').forEach(el => {
      el.classList.add('fade-in');
      el.setAttribute('data-scroll', '');
      el.setAttribute('data-scroll-offset', '30%');
      el.setAttribute('data-scroll-delay', '0.1');
    });

    // Add fade-in-left to left content
    document.querySelectorAll('.welcome-content, .contact-info').forEach(el => {
      el.classList.add('fade-in-left');
      el.setAttribute('data-scroll', '');
      el.setAttribute('data-scroll-offset', '20%');
    });

    // Add fade-in-right to right content
    document.querySelectorAll('.welcome-features, .contact-form-container').forEach(el => {
      el.classList.add('fade-in-right');
      el.setAttribute('data-scroll', '');
      el.setAttribute('data-scroll-offset', '20%');
    });

    // Add staggered animations to cards and list items
    document.querySelectorAll('.service-card, .blog-card, .feature-item, .portfolio-item').forEach((el, index) => {
      el.classList.add('fade-in');
      el.classList.add('fade-in-stagger');
      el.setAttribute('data-scroll', '');
      el.setAttribute('data-scroll-offset', '15%');
    });

    // Add parallax effect to hero backgrounds
    document.querySelectorAll('.hero-section').forEach(el => {
      el.classList.add('parallax-bg');
      el.setAttribute('data-scroll', '');
      el.setAttribute('data-scroll-speed', '-1');
    });
  }

  // Initialize animations
  initAnimations();

  // Add custom class when scrolling and handle animations
  scroll.on('scroll', (instance) => {
    const scrollTop = instance.scroll.y;
    const navbar = document.querySelector('.navbar');

    if (navbar) {
      if (scrollTop > 50) {
        navbar.classList.add('scrolled');
      } else {
        navbar.classList.remove('scrolled');
      }
    }
  });

  // Handle animation classes when elements come into view
  scroll.on('call', (value, way, obj) => {
    if (way === 'enter') {
      obj.el.classList.add('is-inview');
    }
  });
});
