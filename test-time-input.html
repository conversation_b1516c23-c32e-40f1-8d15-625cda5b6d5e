<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Uhrzeiteingabe</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #121212;
            color: #f5f5f5;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #333;
            border-radius: 5px;
            background: #1e1e1e;
            color: #f5f5f5;
            font-size: 16px;
        }
        input[type="text"]:focus {
            border-color: #ff3030;
            outline: none;
        }
        .form-help {
            display: block;
            margin-top: 5px;
            font-size: 14px;
            color: #999;
        }
        .btn {
            background: #ff3030;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #e02828;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #1e1e1e;
            border-radius: 5px;
            border: 1px solid #333;
        }
    </style>
</head>
<body>
    <h1>Test - Uhrzeiteingabe</h1>
    
    <div class="form-group">
        <label for="meeting-time">Gewünschte Uhrzeit *</label>
        <input type="text" id="meeting-time" name="meeting-time" placeholder="z.B. 10:00 Uhr oder 14:30 Uhr" required>
        <small class="form-help">Bitte geben Sie Ihre Wunschzeit für das 15-minütige Gespräch ein.</small>
    </div>
    
    <button type="button" class="btn" onclick="testInput()">Test Eingabe</button>
    
    <div id="result" class="result" style="display: none;">
        <h3>Eingabe:</h3>
        <p id="time-output"></p>
    </div>

    <script>
        function testInput() {
            const timeInput = document.getElementById('meeting-time');
            const result = document.getElementById('result');
            const output = document.getElementById('time-output');
            
            const value = timeInput.value.trim();
            
            if (value) {
                output.textContent = 'Eingegebene Zeit: "' + value + '"';
                result.style.display = 'block';
            } else {
                output.textContent = 'Keine Eingabe gefunden!';
                result.style.display = 'block';
            }
            
            console.log('Eingabe:', value);
        }
        
        // Test beim Laden der Seite
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test-Seite geladen');
            const timeInput = document.getElementById('meeting-time');
            console.log('Time Input Element:', timeInput);
        });
    </script>
</body>
</html>
